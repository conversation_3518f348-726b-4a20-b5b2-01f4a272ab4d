-- CreateEnum
CREATE TYPE "TransportType" AS ENUM ('air', 'rail', 'road', 'sea');

-- CreateEnum
CREATE TYPE "TariffChargeType" AS ENUM ('terminal', 'doc', 'awb', 'custom_clearance', 'pickup', 'other');

-- CreateEnum
CREATE TYPE "TariffChargeUnit" AS ENUM ('kg', 'piece', 'shipment');

-- CreateEnum
CREATE TYPE "TariffMarginType" AS ENUM ('fixed', 'percent');

-- CreateTable
CREATE TABLE "tariffs" (
    "id" TEXT NOT NULL,
    "request_id" TEXT NOT NULL,
    "transport_type" "TransportType" NOT NULL,
    "agent_name" TEXT NOT NULL,
    "carrier_name" TEXT NOT NULL,
    "origin" TEXT NOT NULL,
    "destination" TEXT NOT NULL,
    "transit_time" INTEGER NOT NULL,
    "currency" TEXT NOT NULL,
    "is_starred" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "tariffs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tariff_common_charges" (
    "id" TEXT NOT NULL,
    "tariff_id" TEXT NOT NULL,
    "type" "TariffChargeType" NOT NULL,
    "unit" "TariffChargeUnit" NOT NULL,
    "rate" DECIMAL(65,30) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "tariff_common_charges_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tariff_local_charges" (
    "id" TEXT NOT NULL,
    "tariff_id" TEXT NOT NULL,
    "type" "TariffChargeType" NOT NULL,
    "unit" "TariffChargeUnit" NOT NULL,
    "rate" DECIMAL(65,30) NOT NULL,
    "margin" DECIMAL(65,30) NOT NULL,
    "margin_type" "TariffMarginType" NOT NULL,
    "margin_rate" DECIMAL(65,30) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "tariff_local_charges_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "tariffs" ADD CONSTRAINT "tariffs_request_id_fkey" FOREIGN KEY ("request_id") REFERENCES "requests"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tariff_common_charges" ADD CONSTRAINT "tariff_common_charges_tariff_id_fkey" FOREIGN KEY ("tariff_id") REFERENCES "tariffs"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tariff_local_charges" ADD CONSTRAINT "tariff_local_charges_tariff_id_fkey" FOREIGN KEY ("tariff_id") REFERENCES "tariffs"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
