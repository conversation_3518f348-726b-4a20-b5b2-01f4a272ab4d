import { useState, useEffect } from 'react';
import { useSearch<PERSON>ara<PERSON>, useNavigate } from 'react-router';
import {
  Box,
  Button,
  TextField,
  Select,
  MenuItem,
  FormControl,
  IconButton,

  Collapse,
  CircularProgress,
} from '@mui/material';
import FilterListIcon from '@mui/icons-material/FilterList';
import DeleteIcon from '@mui/icons-material/Delete';
import StarIcon from '@mui/icons-material/Star';
import StarBorderIcon from '@mui/icons-material/StarBorder';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

import { Icon } from '../../../features/create-requests/assets';
import { CustomAlert } from '../../../components';
import { CopyButton } from '../../../features/create-requests/components';
import styles from '../../../new-main-page.module.css';
import { CommonChargesBlock } from './common-charges-block';
import { LocalChargesBlock } from './local-charges-block';
import { Requester } from '../../../lib/requester';
import * as Api from '../../../shared/api';

// API-based data types
type TariffRate = {
  id: string;
  transportType: Api.TariffTransportType;
  agentName: string;
  carrierName: string;
  origin: string;
  destination: string;
  transitTime: number; // in days
  currency: string;
  isStarred: boolean;
  isExpanded: boolean; // UI state, not from API
  commonCharges: TariffCommonCharge[];
  localCharges: TariffLocalCharge[];
  // Calculated fields for display
  total: number;
  margin: number;
  marginType: Api.TariffMarginType;
  sellingRate: number;
  profit: number;
};

type TariffCommonCharge = {
  id: string;
  type: Api.TariffChargeType;
  unit: Api.TariffChargeUnit;
  rate: number;
  // Calculated fields
  total: number;
};

type TariffLocalCharge = {
  id: string;
  type: Api.TariffChargeType;
  unit: Api.TariffChargeUnit;
  rate: number;
  margin: number;
  marginType: Api.TariffMarginType;
  marginRate: number;
  // Calculated fields
  total: number;
  marginTotal: number;
  profit: number;
  useInCalculation: boolean; // UI state
};

export default function TariffsPage() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const requestId = searchParams.get('requestId');

  // Request data state
  const [requestData, setRequestData] = useState<{
    ordinal: string;
    marshroute: string;
    quantity: number;
    weight: number;
    volume: number;
    chargeableWeight: number;
  } | null>(null);
  const [isLoadingRequest, setIsLoadingRequest] = useState(true);
  const [requestError, setRequestError] = useState<string | null>(null);

  // Check for requestId parameter and fetch request data
  useEffect(() => {
    if (!requestId) {
      // Redirect to main page if no requestId
      navigate('/');
      return;
    }

    async function fetchRequestData() {
      try {
        setIsLoadingRequest(true);
        setRequestError(null);

        const response = await Requester.get<Api.Request2>(`/request/${requestId}`);

        if (response.success) {
          const request = response.body;
          setRequestData({
            ordinal: `IN-${String(request.ordinal).padStart(3, "0")}`,
            marshroute: `${request.origin} - ${request.destination}`,
            quantity: request.quantity,
            weight: request.weight,
            volume: request.volume,
            chargeableWeight: request.chargeableWeight
          });
        } else {
          setRequestError('Failed to load request data');
        }
      } catch (error) {
        setRequestError('Error loading request data');
        console.error('Error fetching request:', error);
      } finally {
        setIsLoadingRequest(false);
      }
    }

    fetchRequestData();
  }, [requestId, navigate]);

  // State for text input
  const [text, setText] = useState('');
  const [textError, _setTextError] = useState('');
  const [isPendingSend, _setIsPendingSend] = useState(false);
  const [uploadedFiles, _setUploadedFiles] = useState<File[]>([]);
  const [resultTitle, _setResultTitle] = useState('');
  const [resultContent, _setResultContent] = useState('');

  // State for filters
  const [_transportFilter, _setTransportFilter] = useState('');
  const [_agentFilter, _setAgentFilter] = useState('');

  // Tariff data state
  const [tariffRates, setTariffRates] = useState<TariffRate[]>([]);
  const [isLoadingTariffs, setIsLoadingTariffs] = useState(false);
  const [tariffsError, setTariffsError] = useState<string | null>(null);

  // Helper functions for calculations (moved before useEffect)
  const calculateChargeTotal = (rate: number, unit: Api.TariffChargeUnit, requestData: any) => {
    if (!requestData) return 0;

    switch (unit) {
      case 'kg':
        return rate * requestData.weight;
      case 'piece':
        return rate * requestData.quantity;
      case 'shipment':
        return rate;
      default:
        return 0;
    }
  };

  const calculateMarginTotal = (margin: number, marginType: Api.TariffMarginType, baseRate: number) => {
    return marginType === 'percent' ? (baseRate * margin) / 100 : margin;
  };

  const calculateProfit = (margin: number, marginType: Api.TariffMarginType, baseRate: number) => {
    return calculateMarginTotal(margin, marginType, baseRate);
  };

  // Fetch tariffs for the current request
  useEffect(() => {
    if (!requestId) return;

    async function fetchTariffs() {
      try {
        setIsLoadingTariffs(true);
        setTariffsError(null);

        // Note: This endpoint may not be implemented yet
        // When implemented, it should follow the pattern: GET /api/tariffs?requestId=...
        const response = await Requester.get<Api.GetTariffsResponse>(`/tariffs?requestId=${requestId}`);

        if (response.success) {
          // Transform API response to UI format with calculated fields
          const transformedTariffs: TariffRate[] = response.body.map((tariff, index) => ({
            id: `tariff-${Date.now()}-${index}`, // Temporary ID generation
            transportType: tariff.transportType,
            agentName: tariff.agentName,
            carrierName: tariff.carrierName,
            origin: tariff.origin,
            destination: tariff.destination,
            transitTime: tariff.transitTime,
            currency: 'USD', // Default currency, should come from API
            isStarred: tariff.isStarred,
            isExpanded: false,
            commonCharges: tariff.commonCharges.map((charge, chargeIndex) => ({
              id: `common-${Date.now()}-${index}-${chargeIndex}`,
              type: charge.type,
              unit: charge.unit,
              rate: charge.rate,
              total: calculateChargeTotal(charge.rate, charge.unit, requestData)
            })),
            localCharges: tariff.localCharges.map((charge, chargeIndex) => ({
              id: `local-${Date.now()}-${index}-${chargeIndex}`,
              type: charge.type,
              unit: charge.unit,
              rate: charge.rate,
              margin: charge.margin,
              marginType: charge.marginType,
              marginRate: charge.marginRate,
              total: calculateChargeTotal(charge.rate, charge.unit, requestData),
              marginTotal: calculateMarginTotal(charge.margin, charge.marginType, charge.rate),
              profit: calculateProfit(charge.margin, charge.marginType, charge.rate),
              useInCalculation: true
            })),
            // Calculate totals (simplified calculation)
            total: 0, // Will be calculated
            margin: 0, // Will be calculated
            marginType: 'percent' as Api.TariffMarginType,
            sellingRate: 0, // Will be calculated
            profit: 0 // Will be calculated
          }));

          setTariffRates(transformedTariffs);
        } else {
          // For now, if API is not implemented, show empty state
          setTariffRates([]);
        }
      } catch (error) {
        console.warn('Tariffs API not implemented yet, showing empty state');
        setTariffRates([]);
        setTariffsError(null); // Don't show error for unimplemented API
      } finally {
        setIsLoadingTariffs(false);
      }
    }

    fetchTariffs();
  }, [requestId, requestData]);

  // Helper functions for calculations
  const calculateChargeTotal = (rate: number, unit: Api.TariffChargeUnit, requestData: any) => {
    if (!requestData) return 0;

    switch (unit) {
      case 'kg':
        return rate * requestData.weight;
      case 'piece':
        return rate * requestData.quantity;
      case 'shipment':
        return rate;
      default:
        return 0;
    }
  };

  const calculateMarginTotal = (margin: number, marginType: Api.TariffMarginType, baseRate: number) => {
    return marginType === 'percent' ? (baseRate * margin) / 100 : margin;
  };

  const calculateProfit = (margin: number, marginType: Api.TariffMarginType, baseRate: number) => {
    return calculateMarginTotal(margin, marginType, baseRate);
  };

  // Placeholder functions - no functionality implemented
  const send = () => {
    // No functionality
  };

  const paste = async () => {
    try {
      const clipboardText = await navigator.clipboard.readText();
      setText(prev => prev + (prev ? '\n' : '') + clipboardText);
    } catch (err) {
      console.error('Failed to read clipboard contents: ', err);
    }
  };

  const handleFileUpload = (_event: React.ChangeEvent<HTMLInputElement>) => {
    // No functionality
  };

  const removeFile = (_index: number) => {
    // No functionality
  };

  const getFileExtension = (filename: string) => {
    return filename.split('.').pop()?.toUpperCase() || '';
  };

  const toggleStar = async (id: string) => {
    const tariff = tariffRates.find(rate => rate.id === id);
    if (!tariff) return;

    try {
      // Note: This endpoint may not be implemented yet
      // When implemented: PUT /api/tariffs/:id with { isStarred: !tariff.isStarred }
      const response = await Requester.put(`/tariffs/${id}`, {
        isStarred: !tariff.isStarred
      } satisfies Partial<Api.UpdateTariffRequest>);

      if (response.success) {
        setTariffRates(rates =>
          rates.map(rate =>
            rate.id === id ? { ...rate, isStarred: !rate.isStarred } : rate
          )
        );
      }
    } catch (error) {
      console.warn('Tariff update API not implemented yet');
      // For now, update locally
      setTariffRates(rates =>
        rates.map(rate =>
          rate.id === id ? { ...rate, isStarred: !rate.isStarred } : rate
        )
      );
    }
  };

  const toggleExpand = (id: string) => {
    // This is UI-only state, no API call needed
    setTariffRates(rates =>
      rates.map(rate =>
        rate.id === id ? { ...rate, isExpanded: !rate.isExpanded } : rate
      )
    );
  };

  const deleteRate = async (id: string) => {
    try {
      // Note: This endpoint may not be implemented yet
      // When implemented: DELETE /api/tariffs/:id
      const response = await Requester.delete(`/tariffs/${id}`);

      if (response.success) {
        setTariffRates(rates => rates.filter(rate => rate.id !== id));
      }
    } catch (error) {
      console.warn('Tariff delete API not implemented yet');
      // For now, delete locally
      setTariffRates(rates => rates.filter(rate => rate.id !== id));
    }
  };

  const addCommonCharge = async (rateId: string) => {
    const newCharge: TariffCommonCharge = {
      id: `temp-common-${Date.now()}`,
      type: 'terminal' as Api.TariffChargeType,
      unit: 'shipment' as Api.TariffChargeUnit,
      rate: 0,
      total: 0
    };

    // Update locally first for immediate UI feedback
    setTariffRates(rates =>
      rates.map(rate =>
        rate.id === rateId
          ? {
            ...rate,
            commonCharges: [...rate.commonCharges, newCharge]
          }
          : rate
      )
    );

    try {
      // Note: This would require updating the entire tariff
      // When implemented: PUT /api/tariffs/:id with updated commonCharges
      const tariff = tariffRates.find(rate => rate.id === rateId);
      if (tariff) {
        const response = await Requester.put(`/tariffs/${rateId}`, {
          commonCharges: [...tariff.commonCharges.map(charge => ({
            type: charge.type,
            unit: charge.unit,
            rate: charge.rate
          })), {
            type: newCharge.type,
            unit: newCharge.unit,
            rate: newCharge.rate
          }]
        } satisfies Partial<Api.UpdateTariffRequest>);

        if (!response.success) {
          console.warn('Failed to save common charge to API');
        }
      }
    } catch (error) {
      console.warn('Tariff update API not implemented yet');
    }
  };

  const deleteCommonCharge = async (rateId: string, chargeId: string) => {
    // Update locally first
    setTariffRates(rates =>
      rates.map(rate =>
        rate.id === rateId
          ? {
            ...rate,
            commonCharges: rate.commonCharges.filter(charge => charge.id !== chargeId)
          }
          : rate
      )
    );

    try {
      // Note: This would require updating the entire tariff
      const tariff = tariffRates.find(rate => rate.id === rateId);
      if (tariff) {
        const updatedCharges = tariff.commonCharges
          .filter(charge => charge.id !== chargeId)
          .map(charge => ({
            type: charge.type,
            unit: charge.unit,
            rate: charge.rate
          }));

        const response = await Requester.put(`/tariffs/${rateId}`, {
          commonCharges: updatedCharges
        } satisfies Partial<Api.UpdateTariffRequest>);

        if (!response.success) {
          console.warn('Failed to delete common charge from API');
        }
      }
    } catch (error) {
      console.warn('Tariff update API not implemented yet');
    }
  };

  // Function to synchronize currency across all fields in a tariff
  const synchronizeTariffCurrency = async (rateId: string, newCurrency: string) => {
    // Update locally first
    setTariffRates(rates =>
      rates.map(rate =>
        rate.id === rateId
          ? {
            ...rate,
            currency: newCurrency
          }
          : rate
      )
    );

    try {
      // Note: This would update the tariff currency in the API
      const response = await Requester.put(`/tariffs/${rateId}`, {
        currency: newCurrency
      });

      if (!response.success) {
        console.warn('Failed to update tariff currency in API');
      }
    } catch (error) {
      console.warn('Tariff update API not implemented yet');
    }
  };

  const updateCommonCharge = async (rateId: string, chargeId: string, field: keyof TariffCommonCharge, value: any) => {
    // Update locally first
    setTariffRates(rates =>
      rates.map(rate =>
        rate.id === rateId
          ? {
            ...rate,
            commonCharges: rate.commonCharges.map(charge =>
              charge.id === chargeId
                ? {
                  ...charge,
                  [field]: value,
                  // Recalculate total if rate, unit changed
                  total: field === 'rate' || field === 'unit'
                    ? calculateChargeTotal(
                      field === 'rate' ? value : charge.rate,
                      field === 'unit' ? value : charge.unit,
                      requestData
                    )
                    : charge.total
                }
                : charge
            )
          }
          : rate
      )
    );

    try {
      // Note: This would require updating the entire tariff's commonCharges
      const tariff = tariffRates.find(rate => rate.id === rateId);
      if (tariff) {
        const updatedCharges = tariff.commonCharges.map(charge =>
          charge.id === chargeId
            ? {
              type: field === 'type' ? value : charge.type,
              unit: field === 'unit' ? value : charge.unit,
              rate: field === 'rate' ? value : charge.rate
            }
            : {
              type: charge.type,
              unit: charge.unit,
              rate: charge.rate
            }
        );

        const response = await Requester.put(`/tariffs/${rateId}`, {
          commonCharges: updatedCharges
        } satisfies Partial<Api.UpdateTariffRequest>);

        if (!response.success) {
          console.warn('Failed to update common charge in API');
        }
      }
    } catch (error) {
      console.warn('Tariff update API not implemented yet');
    }
  };

  const addLocalCharge = async (rateId: string) => {
    const newCharge: TariffLocalCharge = {
      id: `temp-local-${Date.now()}`,
      type: 'terminal' as Api.TariffChargeType,
      unit: 'shipment' as Api.TariffChargeUnit,
      rate: 0,
      margin: 0,
      marginType: 'fixed' as Api.TariffMarginType,
      marginRate: 0,
      total: 0,
      marginTotal: 0,
      profit: 0,
      useInCalculation: true
    };

    // Update locally first
    setTariffRates(rates =>
      rates.map(rate =>
        rate.id === rateId
          ? {
            ...rate,
            localCharges: [...rate.localCharges, newCharge]
          }
          : rate
      )
    );

    try {
      // Note: This would require updating the entire tariff
      const tariff = tariffRates.find(rate => rate.id === rateId);
      if (tariff) {
        const response = await Requester.put(`/tariffs/${rateId}`, {
          localCharges: [...tariff.localCharges.map(charge => ({
            type: charge.type,
            unit: charge.unit,
            rate: charge.rate,
            margin: charge.margin,
            marginType: charge.marginType,
            marginRate: charge.marginRate
          })), {
            type: newCharge.type,
            unit: newCharge.unit,
            rate: newCharge.rate,
            margin: newCharge.margin,
            marginType: newCharge.marginType,
            marginRate: newCharge.marginRate
          }]
        } satisfies Partial<Api.UpdateTariffRequest>);

        if (!response.success) {
          console.warn('Failed to save local charge to API');
        }
      }
    } catch (error) {
      console.warn('Tariff update API not implemented yet');
    }
  };

  const deleteLocalCharge = async (rateId: string, chargeId: string) => {
    // Update locally first
    setTariffRates(rates =>
      rates.map(rate =>
        rate.id === rateId
          ? {
            ...rate,
            localCharges: rate.localCharges.filter(charge => charge.id !== chargeId)
          }
          : rate
      )
    );

    try {
      const tariff = tariffRates.find(rate => rate.id === rateId);
      if (tariff) {
        const updatedCharges = tariff.localCharges
          .filter(charge => charge.id !== chargeId)
          .map(charge => ({
            type: charge.type,
            unit: charge.unit,
            rate: charge.rate,
            margin: charge.margin,
            marginType: charge.marginType,
            marginRate: charge.marginRate
          }));

        const response = await Requester.put(`/tariffs/${rateId}`, {
          localCharges: updatedCharges
        } satisfies Partial<Api.UpdateTariffRequest>);

        if (!response.success) {
          console.warn('Failed to delete local charge from API');
        }
      }
    } catch (error) {
      console.warn('Tariff update API not implemented yet');
    }
  };

  const updateLocalCharge = async (rateId: string, chargeId: string, field: keyof TariffLocalCharge, value: any) => {
    // Update locally first with recalculations
    setTariffRates(rates =>
      rates.map(rate =>
        rate.id === rateId
          ? {
            ...rate,
            localCharges: rate.localCharges.map(charge =>
              charge.id === chargeId
                ? {
                  ...charge,
                  [field]: value,
                  // Recalculate totals when relevant fields change
                  total: field === 'rate' || field === 'unit'
                    ? calculateChargeTotal(
                      field === 'rate' ? value : charge.rate,
                      field === 'unit' ? value : charge.unit,
                      requestData
                    )
                    : charge.total,
                  marginTotal: field === 'margin' || field === 'marginType' || field === 'rate'
                    ? calculateMarginTotal(
                      field === 'margin' ? value : charge.margin,
                      field === 'marginType' ? value : charge.marginType,
                      field === 'rate' ? value : charge.rate
                    )
                    : charge.marginTotal,
                  profit: field === 'margin' || field === 'marginType' || field === 'rate'
                    ? calculateProfit(
                      field === 'margin' ? value : charge.margin,
                      field === 'marginType' ? value : charge.marginType,
                      field === 'rate' ? value : charge.rate
                    )
                    : charge.profit
                }
                : charge
            )
          }
          : rate
      )
    );

    try {
      // Note: This would require updating the entire tariff's localCharges
      const tariff = tariffRates.find(rate => rate.id === rateId);
      if (tariff) {
        const updatedCharges = tariff.localCharges.map(charge =>
          charge.id === chargeId
            ? {
              type: field === 'type' ? value : charge.type,
              unit: field === 'unit' ? value : charge.unit,
              rate: field === 'rate' ? value : charge.rate,
              margin: field === 'margin' ? value : charge.margin,
              marginType: field === 'marginType' ? value : charge.marginType,
              marginRate: field === 'marginRate' ? value : charge.marginRate
            }
            : {
              type: charge.type,
              unit: charge.unit,
              rate: charge.rate,
              margin: charge.margin,
              marginType: charge.marginType,
              marginRate: charge.marginRate
            }
        );

        const response = await Requester.put(`/tariffs/${rateId}`, {
          localCharges: updatedCharges
        } satisfies Partial<Api.UpdateTariffRequest>);

        if (!response.success) {
          console.warn('Failed to update local charge in API');
        }
      }
    } catch (error) {
      console.warn('Tariff update API not implemented yet');
    }
  };

  // Show loading or error state
  if (isLoadingRequest) {
    return (
      <Box sx={{ padding: '20px', backgroundColor: '#FFFFFF', minHeight: '100vh', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (requestError || !requestData) {
    return (
      <Box sx={{ padding: '20px', backgroundColor: '#FFFFFF', minHeight: '100vh' }}>
        <Box sx={{ color: 'red', textAlign: 'center', marginTop: '50px' }}>
          {requestError || 'Failed to load request data'}
        </Box>
      </Box>
    );
  }

  return (
    <Box sx={{ padding: '20px', backgroundColor: '#FFFFFF', minHeight: '100vh' }}>
      {/* Request Data Line */}
      <Box sx={{
        backgroundColor: '#FFFFFF',
        padding: '16px',
        borderRadius: '8px',
        marginBottom: '20px',
        fontFamily: "'YS Text', sans-serif",
        position: 'relative',
        display: 'inline-block'
      }}>
        <Box sx={{
          display: 'flex',
          gap: '20px',
          alignItems: 'flex-end'
        }}>
          {/* Ordinal */}
          <Box sx={{ display: 'flex', flexDirection: 'column' }}>
            <Box sx={{
              fontSize: '10px',
              color: '#666666',
              marginBottom: '4px',
              fontWeight: 400
            }}>
              request №
            </Box>
            <Box sx={{
              fontSize: '12px',
              color: '#000000',
              fontWeight: 700
            }}>
              {requestData.ordinal}
            </Box>
          </Box>

          {/* Route */}
          <Box sx={{ display: 'flex', flexDirection: 'column' }}>
            <Box sx={{
              fontSize: '10px',
              color: '#666666',
              marginBottom: '4px',
              fontWeight: 400
            }}>
              Itinerary
            </Box>
            <Box sx={{
              fontSize: '12px',
              color: '#000000',
              fontWeight: 700
            }}>
              {requestData.marshroute}
            </Box>
          </Box>

          {/* Quantity */}
          <Box sx={{ display: 'flex', flexDirection: 'column' }}>
            <Box sx={{
              fontSize: '10px',
              color: '#666666',
              marginBottom: '4px',
              fontWeight: 400
            }}>
              piece
            </Box>
            <Box sx={{
              fontSize: '12px',
              color: '#000000',
              fontWeight: 700
            }}>
              {requestData.quantity}
            </Box>
          </Box>

          {/* Weight */}
          <Box sx={{ display: 'flex', flexDirection: 'column' }}>
            <Box sx={{
              fontSize: '10px',
              color: '#666666',
              marginBottom: '4px',
              fontWeight: 400
            }}>
              weight
            </Box>
            <Box sx={{
              fontSize: '12px',
              color: '#000000',
              fontWeight: 700
            }}>
              {requestData.weight} kg
            </Box>
          </Box>

          {/* Volume */}
          <Box sx={{ display: 'flex', flexDirection: 'column' }}>
            <Box sx={{
              fontSize: '10px',
              color: '#666666',
              marginBottom: '4px',
              fontWeight: 400
            }}>
              Volume
            </Box>
            <Box sx={{
              fontSize: '12px',
              color: '#000000',
              fontWeight: 700
            }}>
              {requestData.volume} m³
            </Box>
          </Box>

          {/* Chargeable Weight */}
          <Box sx={{ display: 'flex', flexDirection: 'column' }}>
            <Box sx={{
              fontSize: '10px',
              color: '#666666',
              marginBottom: '4px',
              fontWeight: 400
            }}>
              Chargeable Weight
            </Box>
            <Box sx={{
              fontSize: '12px',
              color: '#000000',
              fontWeight: 700
            }}>
              {requestData.chargeableWeight} kg
            </Box>
          </Box>
        </Box>

        {/* Horizontal line under the Request Data Line */}
        <Box sx={{
          marginTop: '8px',
          height: '1px',
          backgroundColor: '#C6C5CA',
          width: '100%'
        }} />
      </Box>

      {/* Text Blocks Section */}
      <div className={styles.textContainer}>
        <div className={styles.label}>
          Paste here the text of request for cargo transportation
        </div>

        <button
          onClick={send}
          disabled={isPendingSend || (!text.trim().length && uploadedFiles.length === 0)}
          className={styles.sendButton}
        >
          {isPendingSend ? (
            <CircularProgress
              size={16}
              style={{ position: "absolute", color: "white" }}
            />
          ) : (
            <Icon.Send />
          )}
        </button>

        <div className={styles.textInputsContainer}>
          <CustomAlert message={textError} severity="error" />
          <div className={styles.textIntoInputContainer}>
            <div className={styles.textFieldContainer}>
              <textarea
                className={styles.textField}
                value={text}
                onChange={(e) => setText(e.target.value)}
              />

              {/* File upload container in bottom right corner */}
              <div className={styles.fileUploadContainer}>
                {/* File gallery */}
                <div className={styles.fileGallery}>
                  {uploadedFiles.map((file, index) => (
                    <div key={index} className={styles.fileItem}>
                      <button
                        className={styles.fileRemoveButton}
                        onClick={() => removeFile(index)}
                        title="Remove file"
                      >
                        ×
                      </button>
                      <div className={styles.fileExtension}>
                        {getFileExtension(file.name)}
                      </div>
                      <div className={styles.fileName} title={file.name}>
                        {file.name}
                      </div>
                    </div>
                  ))}
                </div>

                {/* Upload button */}
                <input
                  type="file"
                  multiple
                  className={styles.hiddenFileInput}
                  onChange={handleFileUpload}
                  // accept="*/*"
                  accept=".txt, .docx, .xlsx, .pdf"
                  id="file-upload-input"
                  disabled={uploadedFiles.length >= 3}
                />
                <label
                  htmlFor="file-upload-input"
                  className={styles.fileUploadButton}
                  title={uploadedFiles.length >= 3 ? "Maximum 3 files allowed" : "Upload files"}
                  style={{
                    opacity: uploadedFiles.length >= 3 ? 0.5 : 1,
                    cursor: uploadedFiles.length >= 3 ? 'not-allowed' : 'pointer'
                  }}
                >
                  <Icon.Upload />
                </label>
              </div>
            </div>

            <Button
              disableElevation
              onClick={paste}
              startIcon={<Icon.Paste />}
              size="small"
              variant="text"
              sx={{
                fontSize: "12px",
                textTransform: "lowercase",
                cursor: "pointer",
                fontWeight: "400",
                lineHeight: '16px',
                marginTop: "5px",
              }}
            >
              paste
            </Button>
          </div>

          <div className={styles.resultTextContainer}>
            <Select
              value="wip"
              displayEmpty
              size="small"
              sx={{
                marginBottom: '12px',
                minWidth: '120px',
                fontSize: '12px',
                fontFamily: "'YS Text', sans-serif"
              }}
            >
              <MenuItem value="wip">wip</MenuItem>
            </Select>

            <div className={styles.titleResult}>
              <div style={{ flexShrink: 2, overflow: "auto" }}>{resultTitle}</div>
              <CopyButton text={resultTitle} disabled={!resultTitle} />
            </div>

            <div className={styles.fullTextResultContainer} style={{ height: 'calc(135px - 40px)' }}>
              <div className={styles.fullTextResult}>{resultContent}</div>
              <div className={styles.actionBtns}>
                <CopyButton text={resultContent} disabled={!resultContent} />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Table Section */}
      <Box sx={{
        backgroundColor: '#FFFFFF',
        borderRadius: '8px 8px 0px 0px',
        overflow: 'hidden'
      }}>
        {/* Table Header */}
        <Box sx={{
          backgroundColor: '#70B57D',
          height: '66px',
          display: 'flex',
          alignItems: 'center',
          padding: '0 12px',
          borderRadius: '8px 8px 0px 0px'
        }}>
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            width: '100%'
          }}>
            {/* Transport column with filter */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: '4px', width: '90px', flexShrink: 0 }}>
              <Box sx={{
                fontSize: '12px',
                fontWeight: 700,
                color: '#FFFFFF',
                fontFamily: "'YS Text', sans-serif"
              }}>
                Transport
              </Box>
              <IconButton size="small" sx={{ color: '#FFFFFF' }}>
                <FilterListIcon sx={{ fontSize: '16px' }} />
              </IconButton>
            </Box>

            {/* Agent column with filter */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: '4px', width: '90px', flexShrink: 0 }}>
              <Box sx={{
                fontSize: '12px',
                fontWeight: 700,
                color: '#FFFFFF',
                fontFamily: "'YS Text', sans-serif"
              }}>
                Agent
              </Box>
              <IconButton size="small" sx={{ color: '#FFFFFF' }}>
                <FilterListIcon sx={{ fontSize: '16px' }} />
              </IconButton>
            </Box>

            {/* Direction column */}
            <Box sx={{ width: '180px', flexShrink: 0 }}>
              <Box sx={{
                fontSize: '12px',
                fontWeight: 700,
                color: '#FFFFFF',
                fontFamily: "'YS Text', sans-serif"
              }}>
                Direction
              </Box>
            </Box>

            {/* Carrier column */}
            <Box sx={{ width: '90px', flexShrink: 0 }}>
              <Box sx={{
                fontSize: '12px',
                fontWeight: 700,
                color: '#FFFFFF',
                fontFamily: "'YS Text', sans-serif"
              }}>
                Carrier
              </Box>
            </Box>

            {/* Transit time column */}
            <Box sx={{ width: '90px', flexShrink: 0 }}>
              <Box sx={{
                fontSize: '12px',
                fontWeight: 700,
                color: '#FFFFFF',
                fontFamily: "'YS Text', sans-serif"
              }}>
                Transit time
              </Box>
            </Box>

            {/* Total column */}
            <Box sx={{ width: '110px', flexShrink: 0 }}>
              <Box sx={{
                fontSize: '12px',
                fontWeight: 700,
                color: '#FFFFFF',
                fontFamily: "'YS Text', sans-serif"
              }}>
                Total
              </Box>
            </Box>

            {/* Margin column */}
            <Box sx={{ width: '120px', flexShrink: 0 }}>
              <Box sx={{
                fontSize: '12px',
                fontWeight: 700,
                color: '#FFFFFF',
                fontFamily: "'YS Text', sans-serif"
              }}>
                Margin
              </Box>
            </Box>

            {/* Selling rate column */}
            <Box sx={{ width: '110px', flexShrink: 0 }}>
              <Box sx={{
                fontSize: '12px',
                fontWeight: 700,
                color: '#FFFFFF',
                fontFamily: "'YS Text', sans-serif"
              }}>
                Selling rate
              </Box>
            </Box>

            {/* Profit column */}
            <Box sx={{ width: '110px', flexShrink: 0 }}>
              <Box sx={{
                fontSize: '12px',
                fontWeight: 700,
                color: '#FFFFFF',
                fontFamily: "'YS Text', sans-serif"
              }}>
                Profit
              </Box>
            </Box>

            {/* Actions column */}
            <Box sx={{ width: '120px', flexShrink: 0, display: 'flex', justifyContent: 'flex-end' }}>
              <Box sx={{
                fontSize: '12px',
                fontWeight: 700,
                color: '#FFFFFF',
                fontFamily: "'YS Text', sans-serif"
              }}>
                {/* Actions */}
              </Box>
            </Box>
          </Box>
        </Box>

        {/* Table Body */}
        <Box>
          {tariffRates.map((rate, index) => (
            <Box key={rate.id}>
              {/* Main row */}
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                padding: '0 12px',
                height: '64px',
                justifyContent: 'space-between',
                borderBottom: index < tariffRates.length - 1 ? '1px solid #C6C5CA' : 'none'
              }}>
                {/* Transport */}
                <Box sx={{ width: '90px', flexShrink: 0 }}>
                  <Box sx={{
                    fontSize: '12px',
                    fontWeight: 700,
                    color: '#1A1A1A',
                    fontFamily: "'YS Text', sans-serif"
                  }}>
                    {rate.transportType.toUpperCase()}
                  </Box>
                </Box>

                {/* Agent */}
                <Box sx={{ width: '90px', flexShrink: 0 }}>
                  <Box sx={{
                    fontSize: '12px',
                    fontWeight: 700,
                    color: '#1A1A1A',
                    fontFamily: "'YS Text', sans-serif"
                  }}>
                    {rate.agentName || '-'}
                  </Box>
                </Box>

                {/* Direction */}
                <Box sx={{ width: '180px', flexShrink: 0 }}>
                  <Box sx={{
                    fontSize: '12px',
                    fontWeight: 700,
                    color: '#1A1A1A',
                    fontFamily: "'YS Text', sans-serif"
                  }}>
                    {rate.origin && rate.destination
                      ? `${rate.origin} - ${rate.destination}`
                      : '-'}
                  </Box>
                </Box>

                {/* Carrier */}
                <Box sx={{ width: '90px', flexShrink: 0 }}>
                  <Box sx={{
                    fontSize: '12px',
                    fontWeight: 700,
                    color: '#1A1A1A',
                    fontFamily: "'YS Text', sans-serif"
                  }}>
                    {rate.carrierName || '-'}
                  </Box>
                </Box>

                {/* Transit time */}
                <Box sx={{ width: '90px', flexShrink: 0 }}>
                  <Box sx={{
                    fontSize: '12px',
                    fontWeight: 700,
                    color: '#1A1A1A',
                    fontFamily: "'YS Text', sans-serif"
                  }}>
                    {rate.transitTime} days
                  </Box>
                </Box>

                {/* Total */}
                <Box sx={{ width: '110px', flexShrink: 0, display: 'flex', alignItems: 'center' }}>
                  <Box sx={{
                    fontSize: '12px',
                    fontWeight: 700,
                    color: '#1A1A1A',
                    fontFamily: "'YS Text', sans-serif",
                    marginRight: '4px'
                  }}>
                    {rate.currency}
                  </Box>
                  <Box sx={{
                    fontSize: '12px',
                    fontWeight: 700,
                    color: '#1A1A1A',
                    fontFamily: "'YS Text', sans-serif"
                  }}>
                    {rate.total.toLocaleString()}
                  </Box>
                </Box>

                {/* Margin */}
                <Box sx={{ width: '120px', flexShrink: 0, display: 'flex', alignItems: 'center', gap: '4px' }}>
                  <TextField
                    size="small"
                    value={rate.margin}
                    sx={{
                      width: '60px',
                      '& .MuiOutlinedInput-root': {
                        fontSize: '12px',
                        fontFamily: "'YS Text', sans-serif"
                      }
                    }}
                  />
                  <FormControl size="small" sx={{ minWidth: '40px' }}>
                    <Select
                      value={rate.marginType}
                      sx={{
                        fontSize: '12px',
                        fontFamily: "'YS Text', sans-serif",
                        '& .MuiOutlinedInput-notchedOutline': {
                          border: '1px solid #C6C5CA'
                        }
                      }}
                    >
                      <MenuItem value="fixed">Fixed</MenuItem>
                      <MenuItem value="percent">%</MenuItem>
                    </Select>
                  </FormControl>
                </Box>

                {/* Selling rate */}
                <Box sx={{ width: '110px', flexShrink: 0, display: 'flex', alignItems: 'center' }}>
                  <Box sx={{
                    fontSize: '12px',
                    fontWeight: 700,
                    color: '#1A1A1A',
                    fontFamily: "'YS Text', sans-serif",
                    marginRight: '4px'
                  }}>
                    {rate.currency}
                  </Box>
                  <Box sx={{
                    fontSize: '12px',
                    fontWeight: 700,
                    color: '#1A1A1A',
                    fontFamily: "'YS Text', sans-serif"
                  }}>
                    {rate.sellingRate.toLocaleString()}
                  </Box>
                </Box>

                {/* Profit */}
                <Box sx={{ width: '110px', flexShrink: 0, display: 'flex', alignItems: 'center' }}>
                  <Box sx={{
                    fontSize: '12px',
                    fontWeight: 700,
                    color: '#1A1A1A',
                    fontFamily: "'YS Text', sans-serif",
                    marginRight: '4px'
                  }}>
                    {rate.currency}
                  </Box>
                  <Box sx={{
                    fontSize: '12px',
                    fontWeight: 700,
                    color: '#1A1A1A',
                    fontFamily: "'YS Text', sans-serif"
                  }}>
                    {rate.profit.toLocaleString()}
                  </Box>
                </Box>

                {/* Actions */}
                <Box sx={{ width: '120px', flexShrink: 0, display: 'flex', alignItems: 'center', justifyContent: 'flex-end', gap: '8px' }}>
                  <IconButton
                    size="small"
                    onClick={() => deleteRate(rate.id)}
                    sx={{ color: '#1B1D1F' }}
                  >
                    <DeleteIcon />
                  </IconButton>

                  <IconButton
                    size="small"
                    onClick={() => toggleStar(rate.id)}
                    sx={{ color: rate.isStarred ? '#70B57D' : '#1B1D1F' }}
                  >
                    {rate.isStarred ? <StarIcon /> : <StarBorderIcon />}
                  </IconButton>

                  <IconButton
                    size="small"
                    onClick={() => toggleExpand(rate.id)}
                    sx={{ color: '#1A1A1A', transform: rate.isExpanded ? 'rotate(180deg)' : 'rotate(0deg)' }}
                  >
                    <ExpandMoreIcon />
                  </IconButton>
                </Box>
              </Box>

              {/* Expanded content */}
              <Collapse in={rate.isExpanded}>
                <Box sx={{ padding: '16px', backgroundColor: '#F9F9F9' }}>
                  <Box sx={{ display: 'flex', gap: '20px' }}>
                    {/* Common charges - 40% */}
                    <Box sx={{ flex: '0 0 40%' }}>
                      <CommonChargesBlock
                        charges={rate.commonCharges}
                        onAddCharge={() => addCommonCharge(rate.id)}
                        onDeleteCharge={(chargeId) => deleteCommonCharge(rate.id, chargeId)}
                        onUpdateCharge={(chargeId, field, value) => updateCommonCharge(rate.id, chargeId, field, value)}
                      />
                    </Box>

                    {/* Local charges - 60% */}
                    <Box sx={{ flex: '0 0 60%' }}>
                      <LocalChargesBlock
                        charges={rate.localCharges}
                        onAddCharge={() => addLocalCharge(rate.id)}
                        onDeleteCharge={(chargeId) => deleteLocalCharge(rate.id, chargeId)}
                        onUpdateCharge={(chargeId, field, value) => updateLocalCharge(rate.id, chargeId, field, value)}
                      />
                    </Box>
                  </Box>
                </Box>
              </Collapse>
            </Box>
          ))}
        </Box>
      </Box>
    </Box>
  );
}