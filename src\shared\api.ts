import { z } from "zod";
import { services, countries, digitalSocieties, incoterms } from "./lists";

const id = z.string().nanoid();

function nullableToUndefined<T>(schema: z.ZodSchema<T>) {
    return schema.nullable().optional().transform((val) => val ?? undefined);
}

const _ = nullableToUndefined;

type RequestStatus = z.infer<typeof RequestStatus>;
const RequestStatus = z.enum(["pending", "order", "calculating", "rejected"]);

export type ExtractRequestQuery = Normalize<z.output<typeof ExtractRequestQuery>>;
export const ExtractRequestQuery = z.object({
    type: z.enum(["v1", "v2"]).optional(),
});

export type ExtractRequest = Normalize<z.output<typeof ExtractRequest>>;
export const ExtractRequest = z.object({
    text: z.string(),
});

export type ExtractResponse = Normalize<z.output<typeof ExtractResponse>>;
export const ExtractResponse = z.object({
    service_type: _(z.string()),
    incoterms: _(z.string()),
    origin: _(z.string()),
    destination_country: _(z.string()),

    pickup: _(z.object({
        is_needed: _(z.boolean()),
        city: _(z.string()),
        zip_code: _(z.string()),
        address: _(z.string()),
    })),

    delivery: _(z.object({
        is_needed: _(z.boolean()),
        city: _(z.string()),
        zip_code: _(z.string()),
        address: _(z.string()),
    })),

    summary: z.object({
        piece: _(z.number()),
        weight: _(z.number()),
        volume: _(z.number()),
        density: _(z.number()),
        chargeable_weight: _(z.number()),
    }),

    details: _(z.array(
        z.object({
            piece: _(z.number()),
            dimension: z.object({
                width: z.number().optional(),
                height: z.number().optional(),
                length: z.number().optional(),
                volume: z.number().optional(),
                weight: z.number().optional(),
                is_stackable: z.boolean().optional(),
            }),
        }),
    )),

    additional_details: z.object({
        description_of_goods: _(z.string()),
        hs_codes: _(z.number()),
        costs_of_goods: _(z.number()),
        services: _(z.enum(["express", "standard", "economy"])),
        selected_services: _(z.array(z.string())),
        currency: _(z.string()),
    }),
});

const otp = z.string().length(6).regex(/\d{6}/);

export type OtpRequest = Normalize<z.output<typeof OtpRequest>>;
export const OtpRequest = z.object({
    email: z.string().email(),
    failOnAlreadyRegistered: z.boolean().optional(),
});

export type SignUpRequest = Normalize<z.output<typeof SignUpRequest>>;
export const SignUpRequest = z.object({
    email: z.string().email(),
    password: z.string().min(8).max(256),
    country: z.enum([...countries, ...digitalSocieties]),
    otp,
});

export type SignInRequest = Normalize<z.output<typeof SignInRequest>>;
export const SignInRequest = z.object({
    email: z.string().email(),
    password: z.string().min(8).max(256),
});

export type ResetPasswordRequest = Normalize<z.output<typeof ResetPasswordRequest>>;
export const ResetPasswordRequest = z.object({
    email: z.string().email(),
    password: z.string().min(8).max(256),
    otp,
});

export type CreateRequestRequest = Normalize<z.output<typeof CreateRequestRequest>>;
export const CreateRequestRequest = z.object({
    text: z.string(),

    service: z.enum(services).nullable(),
    incoterms: z.enum(incoterms).nullable(),

    origin: z.object({
        country: z.string().nullable(),
        city: z.string().nullable(),
        address: z.string().nullable(),
        zipcode: z.string().nullable(),
        isPickupRequired: z.boolean(),
    }),

    destination: z.object({
        country: z.string().nullable(),
        city: z.string().nullable(),
        address: z.string().nullable(),
        zipcode: z.string().nullable(),
        isDeliveryRequired: z.boolean(),
    }),

    descriptionOfGoods: z.string().nullable(),
    hsCodes: z.string().nullable(),

    costOfGoods: z.number().nullable(),
    currency: z.string().nullable(),

    additionalServices: z.array(z.string()),
    dangerousGoods: z.array(z.string()),

    quantity: z.number().int().nonnegative(),
    weight: z.number().nonnegative(),
    volume: z.number().nonnegative(),
    chargeableWeight: z.number().nonnegative(),

    packages: z.array(
        z.object({
            quantity: z.number().int().nonnegative(),
            volume: z.number().nonnegative(),
            weight: z.number().nonnegative(),
            length: z.number().nonnegative(),
            width: z.number().nonnegative(),
            height: z.number().nonnegative(),
            type: z.enum(["box", "crate", "pallet"]).nullable(),
            isStackable: z.boolean(),
        }),
    ),
});

export type UpdateRequestStatusRequest = Normalize<z.output<typeof UpdateRequestStatusRequest>>;
export const UpdateRequestStatusRequest = z.object({
    id,
    status: RequestStatus,
});

export type GetRequestsRequest = Normalize<z.output<typeof GetRequestsRequest>>;
export const GetRequestsRequest = z.object({
    origin: z.string().optional(),
    destination: z.string().optional(),

    service: z.enum(services).optional(),

    from: z.coerce.date().optional(),
    to: z.coerce.date().optional(),
});

export type GetRequestRequest = Normalize<z.output<typeof GetRequestRequest>>;
export const GetRequestRequest = z.object({
    id,
});

export type DeleteRequestRequest = Normalize<z.output<typeof DeleteRequestRequest>>;
export const DeleteRequestRequest = z.object({
    id,
});

export type Request = {
    id: string;

    ordinal: number;

    origin: string;
    destination: string;

    service: string;

    quantity: number;
    weight: number;
    volume: number;
    chargeableWeight: number;

    status: RequestStatus;

    createdAt: Date;
    updatedAt: Date;
}

export type Request2 = Normalize<z.infer<typeof CreateRequestRequest>> & {
    id: string;
    ordinal: number;
    status: RequestStatus;
    createdAt: Date;
    updatedAt: Date;
};

const requestTemplateName = z.string().nonempty().max(256);
const requestTemplateTitle = z.string().nonempty().max(1024);
const requestTemplateContent = z.string().nonempty().max(65536);

export type GetRequestTemplatesResponse = Normalize<z.output<typeof GetRequestTemplatesResponse>>;
export const GetRequestTemplatesResponse = z.array(
    z.object({
        id,
        name: requestTemplateName,
        title: requestTemplateTitle,
        content: requestTemplateContent,
    }),
);

export type CreateRequestTemplateRequest = Normalize<z.output<typeof CreateRequestTemplateRequest>>;
export const CreateRequestTemplateRequest = z.object({
    name: requestTemplateName,
    title: requestTemplateTitle,
    content: requestTemplateContent,
});

export type UpdateRequestTemplateRequestBody = Normalize<z.output<typeof UpdateRequestTemplateRequestBody>>;
export const UpdateRequestTemplateRequestBody = z.object({
    name: requestTemplateName.optional(),
    title: requestTemplateTitle.optional(),
    content: requestTemplateContent.optional(),
});

export type UpdateRequestTemplateRequestParams = Normalize<z.output<typeof UpdateRequestTemplateRequestParams>>;
export const UpdateRequestTemplateRequestParams = z.object({
    id,
});

export type DeleteRequestTemplateRequestParams = Normalize<z.output<typeof DeleteRequestTemplateRequestParams>>;
export const DeleteRequestTemplateRequestParams = z.object({
    id,
});

export type TariffTransportType = z.infer<typeof tariffTransportType>;
export const tariffTransportType = z.enum(["air", "sea", "rail", "road"]);

export type TariffChargeType = z.infer<typeof tariffChargeType>;
export const tariffChargeType = z.enum([
    "terminal",
    "doc",
    "awb",
    "custom_clearance",
    "pickup",
    "other",
]);

export type TariffChargeUnit = z.infer<typeof tariffChargeUnit>;
export const tariffChargeUnit = z.enum(["kg", "piece", "shipment"]);

export type TariffMarginType = z.infer<typeof tariffMarginType>;
export const tariffMarginType = z.enum(["fixed", "percent"]);

const tariffCommonCharges = z.array(
    z.object({
        type: tariffChargeType,
        unit: tariffChargeUnit,
        rate: z.number().nonnegative(),
    }),
);

const tariffLocalCharges = z.array(
    z.object({
        type: tariffChargeType,
        unit: tariffChargeUnit,
        rate: z.number().nonnegative(),

        margin: z.number().nonnegative(),
        marginType: tariffMarginType,
        marginRate: z.number().nonnegative(),
    }),
);

export type CreateTariffRequest = Normalize<z.output<typeof CreateTariffRequest>>;
export const CreateTariffRequest = z.object({
    requestId: id,

    transportType: tariffTransportType,

    agentName: z.string().nonempty().max(256),
    carrierName: z.string().nonempty().max(256),

    origin: z.string().nonempty().max(256),
    destination: z.string().nonempty().max(256),

    transitTime: z.number().int().positive(),

    commonCharges: tariffCommonCharges,
    localCharges: tariffLocalCharges,
});

export type CreateTariffResponse = Normalize<z.output<typeof CreateTariffResponse>>;
export const CreateTariffResponse = z.object({
    id,
});

export type UpdateTariffRequest = Normalize<z.output<typeof UpdateTariffRequest>>;
export const UpdateTariffRequest = z.object({
    id,

    transportType: tariffTransportType.optional(),

    agentName: z.string().nonempty().max(256)
        .optional(),
    carrierName: z.string().nonempty().max(256)
        .optional(),

    origin: z.string().nonempty().max(256)
        .optional(),
    destination: z.string().nonempty().max(256)
        .optional(),

    transitTime: z.number().int().positive()
        .optional(),

    commonCharges: tariffCommonCharges.optional(),
    localCharges: tariffLocalCharges.optional(),

    isStarred: z.boolean().optional(),
});

export type GetTariffsRequest = Normalize<z.output<typeof GetTariffsRequest>>;
export const GetTariffsRequest = z.object({
    requestId: id,
});

export type GetTariffsResponse = Normalize<z.output<typeof GetTariffsResponse>>;
export const GetTariffsResponse = z.array(
    z.object({
        transportType: tariffTransportType,

        agentName: z.string().nonempty().max(256),
        carrierName: z.string().nonempty().max(256),

        origin: z.string().nonempty().max(256),
        destination: z.string().nonempty().max(256),

        transitTime: z.number().int().positive(),

        commonCharges: tariffCommonCharges,
        localCharges: tariffLocalCharges,

        isStarred: z.boolean(),
    }),
);

export type DeleteTariffRequest = Normalize<z.output<typeof DeleteTariffRequest>>;
export const DeleteTariffRequest = z.object({
    id,
});
