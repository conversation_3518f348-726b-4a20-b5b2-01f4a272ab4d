import { z } from "zod";
import * as Api from "../../shared/api";

export const transportType = Api.tariffTransportType;

export const chargeType = Api.tariffChargeType;

export const chargeUnit = Api.tariffChargeUnit;

export const Extract = z.object({
    variants: z.array(
        z.object({
            transportType,

            origin: z.string().nonempty().nullable(),
            destination: z.string().nonempty().nullable(),

            agentName: z.string().nonempty().nullable(),
            carrierName: z.string().nonempty().nullable(),

            currency: z.string().nonempty(),

            charges: z.array(
                z.object({
                    type: chargeType.nullable(),
                    unit: chargeUnit.nullable(),
                    rate: z.number().nonnegative(),
                    currency: z.string().nonempty(),
                }),
            ),

            localCharges: z.array(
                z.object({
                    type: chargeType.nullable(),
                    unit: chargeUnit.nullable(),
                    rate: z.number().nonnegative(),
                    currency: z.string().nonempty(),
                }),
            ),
        }),
    ),
});
